import sys
import os
sys.path.append('..')
sys.path.append('connectors')
sys.path.append(os.path.join(os.path.dirname(__file__), '../connectors'))
from openai import OpenAI
import time
from pathlib import Path
from pygame import mixer
from memory_bridge import MemoryBridge

tts_enabled = True

# Initialize the client
client = OpenAI(api_key="********************************************************************************************************************************************************************")
mixer.init()
# Retrieve the assistant
assistant = client.beta.assistants.retrieve("asst_X4bTYziTEgz2CqqFLs58UIus")
# Create empty thread
jarvis_thread = "thread_nezuUpNHEUj72Sy9jqzmi24W"
thread = client.beta.threads.retrieve(jarvis_thread)

def ask_question_memory(question):
    global thread
    global thread_message
    
    # Set memory
    MemoryBridge.set_memory(question)
    
    # Get relevant memories
    relevant_memories = MemoryBridge.get_memory(question)
    
    # Combine question with relevant memories
    combined_input = f"User Question: {question}\n [{relevant_memories}] "
    
    thread_message = client.beta.threads.messages.create(
        thread.id,
        role="user",
        content=combined_input,
    )
    
    # Create a run for the thread
    run = client.beta.threads.runs.create(
      thread_id=thread.id,
      assistant_id=assistant.id,
    )
    
    # Wait for the run to complete
    while True:
        run_status = client.beta.threads.runs.retrieve(
          thread_id=thread.id,
          run_id=run.id
        )
        if run_status.status == 'completed':
            break
        elif run_status.status == 'failed':
            return "The run failed."
        time.sleep(1)  # Wait for 1 second before checking again
    
    # Retrieve messages after the run has succeeded
    messages = client.beta.threads.messages.list(
      thread_id=thread.id
    )
    return messages.data[0].content[0].text.value

def play_sound(file_path):
    mixer.music.load(file_path)
    mixer.music.play()
    
def TTS(text):
    speech_file_path = Path(f"speech.mp3")
    speech_file_path = generate_tts(text, speech_file_path)
    play_sound(speech_file_path)
    while mixer.music.get_busy():  # Wait for the mixer to finish
        time.sleep(1)
    mixer.music.unload()
    # Delete the file after playing
    os.remove(speech_file_path)
    return "done"

def generate_tts(sentence, speech_file_path):
    response = client.audio.speech.create(
        model="tts-1",
        voice="echo",
        input=sentence,
    )
    response.stream_to_file(speech_file_path)
    return str(speech_file_path)